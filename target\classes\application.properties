# PRINCIPAL
# Configuraci�n base para todos los entornos
spring.application.name=crm

# Por defecto, usar el perfil de desarrollo
# Para cambiar a producci�n, usar: -Dspring.profiles.active=prod
spring.profiles.active=prod

# Permitir dependencias circulares (soluci�n temporal)
spring.main.allow-circular-references=true

# Configuraci�n de cache con Redis
spring.cache.type=redis
spring.cache.redis.time-to-live=1800000
spring.cache.redis.cache-null-values=false
spring.cache.redis.key-prefix=midas:crm:
spring.cache.redis.use-key-prefix=true

# JWT
app.jwt.secret=RandomSecretKey123456789!RandomSecretKey123456789!RandomSecretKey123456789!
app.jwt.expiration-in-ms=561600000

# Seguridad por clave secreta (servicio interno)
service.security.secure-key-username=vaxiDrezKeySecureUsername
service.security.secure-key-password=vaxiDrezKeySecurePassword!

# Rutas de API para controladores
# Autenticaci�n
api.route.authentication=/api/authentication

# Usuarios
api.route.user=/api/user

# Clientes
api.route.clientes=/api/clientes
api.route.cliente-promocion=/api/cliente-promocion

# Anuncios
api.route.anuncios=/api/anuncios

api.route.transcriptionanalysis=/api/transcription-analysis
# Manuales
api.route.manuales=/api/manuales

# Coordinadores
api.route.coordinadores=/api/coordinadores

# Asesores
api.route.asesores=/api/asesores

# Estad�sticas por sede
api.route.estadisticas-sede=/api/estadisticas-sede

# Calendario
api.route.calendar=/api/calendar

# Asistencias
api.route.asistencias=/api/asistencias

# Mensajer�a
api.route.sms=/api/sms
api.route.fcm=/api/fcm
api.route.messaging-token=/api/registerMessagingToken
api.route.numbers=/api/numbers
api.route.bulk=/api/bulk

# IP permitidas
api.route.ip-allowed=/api/ip-allowed

# Notificaciones
api.route.notifications=/api/notifications

# FAQs
api.route.faqs=/api/faqs
api.route.faq-respuestas=/api/faq-respuestas

# Monitoreo del sistema
api.route.system.connections=/api/system/connections

# CURSOS
api.route.curso=/api/cursos
api.route.modulo=/api/modulos
api.route.leccion=/api/lecciones
api.route.progreso=/api/progreso
api.route.secciones=/api/secciones
api.route.video-info=/api/video-info
api.route.cursos-usuarios=/api/cursos-usuarios

# CUESTIONARIOS
api.route.cuestionario=/api/cuestionarios
api.route.pregunta=/api/preguntas
api.route.respuesta=/api/respuestas
api.route.respuesta-usuario=/api/respuestas-usuario

# CATASTRO
api.route.catastro=/api/catastro

# ENCUESTAS
api.route.encuestas=/api/encuestas
api.route.preguntas-encuesta=/api/preguntas-encuesta
api.route.respuestas-encuesta=/api/respuestas-encuesta

# ROLES Y RUTAS
api.route.roles=/api/roles
api.route.routes=/api/routes

# Google Drive API
api.route.google-drive=/api/google-drive
api.route.transcription-queue=/api/transcription-queue
api.route.baseDatosTranscripcion=/api/transcriptor-test
# Embed Controller
api.route.embed=/embed
api.route.independent-transcriptions=/api/independent-transcriptions

# Google Drive Configuration
google.drive.auth.type=oauth
google.drive.credentials.file=client_secret_com.json
google.drive.service-account.file=audios-461713-c7a956b2b4ef.json

# Configuraci�n para compartir autom�tico
google.drive.shared.email=<EMAIL>
google.drive.auto-share.enabled=false

# ========================================
# CONFIGURACI�N DE CONVERSI�N DE AUDIO CON ORGANIZACI�N
# ========================================

# Configuraci�n de conversi�n de audio
audio.conversion.temp.dir=${java.io.tmpdir}/audio-conversion
audio.conversion.ffmpeg.path=jave2-embedded
audio.conversion.upload.to.drive=true
audio.conversion.drive.folder.name=CONVERTED_MP3
google.drive.auto-organize.enabled=true
# Configuraci�n de calidad optimizada para transcripci�n
audio.conversion.optimized.enabled=true
audio.conversion.bitrate=64000
audio.conversion.sample.rate=16000
audio.conversion.channels=1
# Configuraci�n de nombres de archivo
audio.conversion.filename.include.date=true
audio.conversion.filename.include.agent=true
audio.conversion.filename.include.mobile=true
audio.conversion.filename.include.leadid=true
# ID de carpeta padre compartida (opcional)
# Esta carpeta debe ser creada manualmente y compartida con ambas cuentas
google.drive.shared.parent.folder.id=

# Configuraci�n de conexi�n
spring.rabbitmq.connection-timeout=30000
spring.rabbitmq.requested-heartbeat=60

# Configuraci�n de publisher
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true

# Configuraci�n de listener
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.max-attempts=3

spring.rabbitmq.listener.simple.acknowledge-timeout=7200000
spring.rabbitmq.template.receive-timeout=7200000

# ===== CONFIGURACI�N DE APIS EXTERNAS =====
# URLs de las APIs de transcripci�n y comparaci�n
transcription.api.url=https://gni8cguopmely9-8000.proxy.runpod.net/api
comparison.api.url=https://apisozarusac.com/ventas/api

# Configuraci�n de timeouts para APIs externas (en segundos)
comparison.api.timeout=300000

# ===== CONFIGURACI�N OPTIMIZADA PARA TRANSCRIPCIONES R�PIDAS =====

# TIMEOUTS INTELIGENTES MEJORADOS - M�s agresivos para audios peque�os
transcription.poll.initial-wait-seconds=60
transcription.poll.interval-seconds=20
transcription.poll.max-attempts=15

# CONFIGURACI�N POR TAMA�O DE ARCHIVO OPTIMIZADA
# Archivos muy peque�os (< 5 MB): ~2-5 min de audio - S�PER R�PIDOS
transcription.tiny-file.max-size-mb=5
transcription.tiny-file.initial-wait-seconds=30
transcription.tiny-file.early-check-seconds=15
transcription.tiny-file.interval-seconds=20
transcription.tiny-file.max-attempts=8

# Archivos peque�os (5-10 MB): ~5-10 min de audio - R�PIDOS
transcription.small-file.max-size-mb=10
transcription.small-file.initial-wait-seconds=60
transcription.small-file.interval-seconds=25
transcription.small-file.max-attempts=10

# Archivos medianos (10-50 MB): ~10-30 min de audio - NORMALES
transcription.medium-file.max-size-mb=50
transcription.medium-file.initial-wait-seconds=90
transcription.medium-file.interval-seconds=30
transcription.medium-file.max-attempts=12

# Archivos grandes (> 50 MB): ~30+ min de audio - LENTOS
transcription.large-file.initial-wait-seconds=180
transcription.large-file.interval-seconds=45
transcription.large-file.max-attempts=16

# CONFIGURACI�N DE VERIFICACI�N TEMPRANA
transcription.early-check.enabled=true
transcription.early-check.interval-seconds=15
transcription.early-check.max-attempts=3

# CONFIGURACI�N DE FILTRADO OPTIMIZADO
transcription.filter.parallel-threshold=200
transcription.filter.batch-size=1000
transcription.filter.enable-caching=true

# CONFIGURACI�N DE REST TEMPLATES OPTIMIZADOS
transcription.fast-api.connect-timeout=5000
transcription.fast-api.read-timeout=10000
transcription.standard-api.connect-timeout=10000
transcription.standard-api.read-timeout=30000

# CONFIGURACI�N DE B�SQUEDA OPTIMIZADA
transcription.search.max-retries=3
transcription.search.retry-delay-seconds=5
transcription.search.enable-parallel=true

# CONFIGURACI�N DE MONITOREO Y LOGS
transcription.monitoring.log-performance=true
transcription.monitoring.log-timing=true
transcription.monitoring.alert-slow-threshold-seconds=300

# CONFIGURACI�N DE RABBIT MQ OPTIMIZADA PARA CHANNEL SAFETY
spring.rabbitmq.listener.simple.recovery-interval=5000
spring.rabbitmq.listener.simple.missing-queues-fatal=false
spring.rabbitmq.listener.simple.auto-startup=true

# CONFIGURACI�N DE CACH� PARA FILTRADO
transcription.cache.leads-index.ttl-minutes=30
transcription.cache.audio-files.ttl-minutes=15
transcription.cache.enable-local-cache=true

# CONFIGURACI�N DE PROCESAMIENTO AS�NCRONO
transcription.async.core-pool-size=5
transcription.async.max-pool-size=15
transcription.async.queue-capacity=100
transcription.async.keep-alive-seconds=60


api.route.gmail=/api/gmail

# TIMEOUTS HTTP PARA PETICIONES LARGAS
spring.mvc.async.request-timeout=7200000
server.servlet.session.timeout=7200s

# ==================================================
# CONFIGURACI�N DE M�TRICAS CON ACTUATOR Y MICROMETER
# ==================================================
# Exponer el endpoint de Prometheus para que sea "scrapeado" (le�do)
management.endpoints.web.exposure.include=prometheus,health,info

# A�adir tags a todas las m�tricas para una mejor identificaci�n en Grafana
management.metrics.tags.application=${spring.application.name}
management.metrics.tags.environment=call-center
management.metrics.tags.service=crm-backend

# Mostrar detalles completos en el endpoint de salud cuando se est� autorizado
management.endpoint.health.show-details=when_authorized

# Configuraci�n de correo
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=olvhadccjyeixcvj
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.protocols=TLSv1.2
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.smtp.ssl.ciphers=TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
spring.mail.properties.mail.smtp.connectiontimeout=3000
spring.mail.properties.mail.smtp.timeout=3000
spring.mail.properties.mail.smtp.writetimeout=3000

google.oauth.client-id=894231481722-q61kh9ohn2dl584dh3iotq4dhvvs3jol.apps.googleusercontent.com
google.oauth.client-secret=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