package com.midas.crm.controller;

import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.GoogleOAuthService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequiredArgsConstructor
@Slf4j
public class OAuth2CallbackController {

    private final GoogleOAuthService googleOAuthService;
    private final UserRepository userRepository;

    // Endpoint para autorizar la cuenta de un usuario de GMAIL
    @GetMapping("/oauth2/authorize/user")
    public void initiateUserAuth(@AuthenticationPrincipal UserDetails userDetails, HttpServletResponse response) throws IOException {
        if (userDetails == null) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Acceso no autorizado.");
            return;
        }
        User crmUser = userRepository.findByUsername(userDetails.getUsername()).orElseThrow(() -> new IllegalStateException("Usuario no encontrado."));
        String url = googleOAuthService.getAuthorizationUrl(crmUser.getId().toString());
        response.sendRedirect(url);
    }

    // Endpoint para autorizar la cuenta GLOBAL de DRIVE
    @GetMapping("/oauth2/authorize/global")
    public void initiateGlobalAuth(HttpServletResponse response) throws IOException {
        // Aquí se podría añadir una validación para que solo un admin pueda hacer esto
        log.info("🔗 Iniciando autorización OAuth global para Google Drive...");
        try {
            String url = googleOAuthService.getAuthorizationUrl(GoogleOAuthService.GLOBAL_DRIVE_TOKEN_ID);
            log.info("✅ URL de autorización generada exitosamente: {}", url);
            response.sendRedirect(url);
        } catch (Exception e) {
            log.error("❌ Error al generar URL de autorización: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error al generar URL de autorización: " + e.getMessage());
        }
    }

    // Endpoint para PROBAR la generación de URL sin redirección
    @GetMapping("/oauth2/test-url")
    public ResponseEntity<String> testAuthUrl() {
        try {
            log.info("🧪 Probando generación de URL de autorización OAuth...");
            String url = googleOAuthService.getAuthorizationUrl(GoogleOAuthService.GLOBAL_DRIVE_TOKEN_ID);

            String response = String.format(
                "✅ URL de autorización generada exitosamente:\n\n%s\n\n" +
                "Para autorizar, copia y pega esta URL en tu navegador o usa: /oauth2/authorize/global",
                url
            );

            return ResponseEntity.ok()
                    .header("Content-Type", "text/plain; charset=UTF-8")
                    .body(response);
        } catch (Exception e) {
            log.error("❌ Error al generar URL de prueba: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("❌ Error: " + e.getMessage());
        }
    }

    @GetMapping("/oauth2/callback")
    public ResponseEntity<String> handleOAuth2Callback(
            @RequestParam(value = "code") String code,
            @RequestParam(value = "state") String state,
            @RequestParam(value = "error", required = false) String error) {

        if (error != null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Acceso denegado por el usuario.");
        }
        try {
            googleOAuthService.processAuthorizationCode(state, code);

            // --- HTML MEJORADO CON FAVICON ---
            String faviconDataUri = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0OCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjREFBNTIwIiBzdHJva2Utd2lkdGg9IjQiLz48dGV4dCB4PSI1MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJHZW9yZ2lhLCBzZXJpZiIgZm9udC1zaXplPSI2MCIgZm9udC13ZWlnaHQ9ImJvbGQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM0QTRENEEiPk08L3RleHQ+PC9zdmc+";

            String successHtml = """
                <!DOCTYPE html>
                <html lang="es">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Autorización Exitosa - MIDAS CRM</title>
                    <link rel="icon" type="image/svg+xml" href="%s">
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: 20px; }
                        .container { background: white; border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); max-width: 500px; width: 100%; animation: slideUp 0.6s ease-out; }
                        @keyframes slideUp { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
                        .success-icon { width: 80px; height: 80px; background: linear-gradient(135deg, #4CAF50, #45a049); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 30px; animation: pulse 2s infinite; }
                        @keyframes pulse { 0%% { transform: scale(1); } 50%% { transform: scale(1.05); } 100%% { transform: scale(1); } }
                        .checkmark { color: white; font-size: 40px; font-weight: bold; }
                        h1 { color: #333; font-size: 28px; margin-bottom: 15px; font-weight: 600; }
                        p { color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px; }
                        .close-button { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 30px; border-radius: 25px; font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); }
                        .close-button:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4); }
                        .logo { width: 120px; margin-bottom: 20px; }
                        @media (max-width: 480px) { .container { padding: 30px 20px; } h1 { font-size: 24px; } .success-icon { width: 60px; height: 60px; } .checkmark { font-size: 30px; } }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="success-icon"><div class="checkmark">✓</div></div>
                        <h1>¡Autorización Exitosa!</h1>
                        <p>Tu cuenta de Google ha sido conectada exitosamente al CRM MIDAS.<br>Ya puedes cerrar esta ventana y continuar usando el sistema.</p>
                        <button class="close-button" onclick="window.close()">Cerrar Ventana</button>
                        <script>
                            if (window.opener) { setTimeout(() => { window.close(); }, 5000); }
                            let countdown = 5;
                            const button = document.querySelector('.close-button');
                            if (window.opener) {
                                const interval = setInterval(() => {
                                    countdown--;
                                    button.textContent = `Cerrando en ${countdown}s`;
                                    if (countdown <= 0) { clearInterval(interval); window.close(); }
                                }, 1000);
                            }
                        </script>
                    </div>
                </body>
                </html>
                """.formatted(faviconDataUri);

            return ResponseEntity.ok()
                    .header("Content-Type", "text/html; charset=UTF-8")
                    .body(successHtml);
        } catch (Exception e) {
            log.error("Error al procesar el código de autorización de Google: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("Error interno al procesar la autorización.");
        }
    }
}
